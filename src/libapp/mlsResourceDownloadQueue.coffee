debugHelper = require '../lib/debug'
debug = debugHelper.getDebugger()
DOWNLOAD_ALLOWED_MS = 30*1000 # 30 seconds
DEFAULT_DOWNLOAD_START_TS = new Date('1970-01-01T00:00:00.0Z')

class ResourceDownloadQueue
  constructor: (@resourceName, @queueCol) ->
    # await @ensureIndexes()

  ensureIndexes: () ->
    await @queueCol.createIndex { dlShallEndTs: 1, priority: -1 }, { background: true }

  # Add a resource to the download queue
  addToQueue: (record, priority = null) ->
    if not record?._id
      debug.error "#{@resourceName} addToQueue: record._id not found",record
      return false
    if (not priority) or (typeof priority isnt 'number') or (priority < 0)
      debug.error "#{@resourceName} addToQueue: invalid priority value", priority, record
      return false
    doc = {
      _id: record._id
      priority: priority
      dlShallEndTs: DEFAULT_DOWNLOAD_START_TS
    }
    await @queueCol.updateOne(
      {_id: record._id},
      {$set: doc},
      {upsert: true}
    )

  # Get next batch of resources to download  
  getNextBatch: (batchSize = 100) ->
    results = await @queueCol.findToArray(
      {dlShallEndTs: {$lt: new Date()}},
      {sort: {priority: -1}, limit: batchSize}
    )
    if results.length > 0
      # Update dlShallEndTs for all records in the batch
      ids = results.map((record) -> record._id)
      await @queueCol.updateMany(
        {_id: {$in: ids}},
        {$set: {dlShallEndTs: new Date(Date.now() + DOWNLOAD_ALLOWED_MS)}}
      )
      return results
    null


  getNext: () ->
    result = await @queueCol.findOneAndUpdate(
      {dlShallEndTs:{$lt:new Date()}},
      {$set:{dlShallEndTs:new Date(Date.now() + DOWNLOAD_ALLOWED_MS)}},
      {sort:{priority:-1}})
    if result.value?
      return result.value
    null


  removeFromQueue: (record) ->
    await @queueCol.deleteOne({
      _id: record._id
    })
  

  removeBatchFromQueue: (records) ->
    if not records?.length
      return
    await @queueCol.deleteMany({
      _id: { $in: records.map((record) -> record._id) }
    })
  

module.exports = ResourceDownloadQueue


