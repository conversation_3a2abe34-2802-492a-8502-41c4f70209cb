###
  description: watch properties change and translate remarks
  Options:
    force: ignore running watch
  Usage:
    nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndTranslate.coffee preload-model > ./logs/watchTranslate.log 2>&1 &
  New Config:
    ./start.sh -t batch -n watchPropAndTranslate -cmd 'lib/batchBase.coffee batch/prop/watchPropAndTranslate.coffee preload-model'
  #NOTE：
  1.不是所有translate的接口都有限制
  2.其他app内也会调用翻译
  3.随时会切换translate的提供方，应该只改动gDefaultBatchTranslateServiceNameList值

###
helpersFunction = INCLUDE 'lib.helpers_function'
watchHelper = INCLUDE 'libapp.watchHelper'
libStaticListing = INCLUDE 'model.staticRMlisting'
translatorManagerLib = INCLUDE 'lib.translator/translatorManager'
speed = INCLUDE 'lib.speed'

conf = CONFIG(['azure','deepseek','deepL','openAI','gemini','claude','grok','rm'])
debug = DEBUG debugLevel
avgs   = AVGS
PropertiesModel = MODEL 'Properties'
PropertiesCol = COLLECTION('vow', 'properties')
SysdataCol = COLLECTION('vow','sysdata')
ProcessStatusCol = COLLECTION('vow','processStatus')

HALF_DAY_IN_MS = 12 * 3600 * 1000
debugLevel = if avgs.indexOf('debug') >= 0 then 3 else 1
isDryRun = 'dry' in avgs
UPDATE_PROCESS_STATE_INTERNAL = 10 * 60 * 1000
translatorManager = translatorManagerLib.createTranslatorManager(conf)
PROCESS_STATUS_ID = 'watchPropAndTranslate'
gDefaultBatchTranslateServiceNameList = ['rm','gemini']
gDefaultBatchUserId = 'batch'

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 100,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}
gUpdateSysData = helpersFunction.addAsyncSupport (watchHelper.getUpdateSysdataFunction({
  mongo:1,
  SysData:SysdataCol,
  sysDataId:PROCESS_STATUS_ID
}))
{
  processKillSignal,
  RUNNING_NORMAL,
  getProcessHandler,
  HAS_RUNNING_PROCESS
} = require '../../libapp/processHelper'
{
  handleProcessStatus,
  checkRunningProcess,
  handleExit,
} = getProcessHandler {
  ProcessStatusCol,
  id: PROCESS_STATUS_ID,
  filePath: BATCH_FILE_NAME,
  exitFn: EXIT
  updateSysData:gUpdateSysData
}
gWatchedObject = null
#kill SIGUSR1 $pid
# this does not infect debug in watchHelper, different instance.
process.on 'SIGUSR1', ->
  debugHelper.increseModuleThreshhold(1)
process.on 'SIGUSR2', ->
  debugHelper.increseModuleThreshhold(-1)
debug.info 'BATCH_FILE_NAME',BATCH_FILE_NAME

gracefulExit = (error)->
  debug.error 'gracefulExit error', error if error
  if gWatchedObject
    gWatchedObject.end()
  handleExit {error,watchedObject: gWatchedObject,exitFn:EXIT}
processKillSignal PROCESS_STATUS_ID,gracefulExit
process.on 'uncaughtException', gracefulExit

# Process a single translation result and update database
processTranslationResult = (translation, propId, src) ->
  if not translation or ('string' isnt typeof translation)
    debug.error 'Invalid translation response:', translation,propId
    speedMeter.check {'translateFailed': 1}
    return
    
  speedMeter.check {'translated': 1}
  rmlog = {
    ts: new Date(),
    engine: src,
    id: propId,
  }
  debug.info 'updateMZh', rmlog
  
  vals = {
    m_zh: translation,
    propId: propId,
  }
  
  try
    ret = await PropertiesModel.updateMZh vals
    speedMeter.check {'updateMZhSuccess': 1}
  catch error
    debug.error 'Error updating translation:', {
      error,
      propId: propId,
      translation: translation
    }

_insertOne = (prop, cb)->
  if isDryRun
    debug.info 'dryRun insertOne',prop
    return cb()
  unless prop?._id
    debug.error 'invalid prop', prop
    return cb 'no id'
  if not (prop?.m)
    speedMeter.check {'propNoM': 1}
    debug.warn 'invalid prop no remarks m', prop._id
    return cb ''
  if not prop.prov in ['ON','BC','AB']
    speedMeter.check {'propNotONorBC': 1}
    return cb()
  # Check if property is older than 2 years
  twoYearsAgo = new Date()
  twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2)
  if prop.mt < twoYearsAgo
    speedMeter.check {'propMTTooOld': 1}
    return cb()
  if prop.m_zh or prop.merged or prop.ddfID or \
    (prop.ptype isnt 'r') or (prop.status isnt 'A')
    speedMeter.check {'skippedDueToConditions': 1}
    return cb()
    
  # Function to replace text content
  replaceM = (m='') ->
    if not m
      return m
    m = m+''
    for i in libStaticListing.propMReplaceList
      m = m.replace i.reg, i.to
    return m
    
  toBeTranslatedStr = replaceM(prop.m)
  if not toBeTranslatedStr
    speedMeter.check {'skiped':1}
    return cb()

  # Translate and update database
  try
    [translation,src] = await translatorManager.translate(toBeTranslatedStr,gDefaultBatchTranslateServiceNameList)
    await processTranslationResult(translation, prop._id, src)
    speedMeter.check {"translateSuccess_#{src or 'unknown'}": 1}
  catch error
    debug.error 'Translation failed:', {
      error,
      propId: prop._id
    }
    speedMeter.check {"translateFailed_#{src or 'unknown'}": 1}
  cb()

_replaceOne = (prop, cb)->
  _insertOne prop, cb

_updateOne = (prop, cb)->
  _insertOne prop, cb

_deleteOne = (id,cb)->
  return cb()

# update process status
updateProcessStatusForWatch = (startTs)->
  debug.info 'update Process Status'
  try
    await handleProcessStatus {
      startTs: startTs
      status: RUNNING_NORMAL,
      nextTs: Date.now()+UPDATE_PROCESS_STATE_INTERNAL,
    }
  catch error
    debug.error 'updateProcessStatusForWatch',error

lastTokenPrintTs = new Date()
gStat = {}
# update token and process status
onTokenUpdate = ({token,tokenClusterTs,tokenChangeTs,end,resumeMt})->
  debug.info 'onTokenUpdate',token,\
    'gStat:',gStat,'from:',lastTokenPrintTs
  lastTokenPrintTs = new Date(tokenClusterTs)
  gStat = {}
  try
    await gUpdateSysData {token,tokenClusterTs,tokenChangeTs,end,resumeMt}
  catch error
    debug.critical error
    return gracefulExit error

# watch callback
onChange = (changedObj, watchedColl, cb)->
  gStat[changedObj.operationType]?=0
  gStat[changedObj.operationType]++
  
  watchHelper.processChangedObject {
    changedObj,
    watchedColl,
    deleteOneFn:_deleteOne,
    insertOneFn:_insertOne,
    replaceOneFn:_replaceOne,
    updateOneFn:_updateOne,
    # TODO:
    # updateOneFnWithUpdateFields
    watchedStream: gWatchedObject,
    }, (err, ret)->
      if err
        debug.critical 'processChangedObject error',err
      cb err, ret

onWatchError = (err)->
  if err
    debug.error 'watchProp error', err
    return gracefulExit err

# watch and process one when change.
#aviod sent listings too old,tokenClusterTs should within 12 hours.
watchProp = (token,tokenClusterTs,resumeMt) ->
  if resumeMt
    startTs = new Date(resumeMt)
  else
    startTs = new Date(tokenClusterTs or new Date())
  query ={mt:{$gte:startTs}}
  opt = {
    watchedColl: PropertiesCol,
    onChange: onChange,
    onTokenUpdate: onTokenUpdate,
    queryWhenInvalidToken:query,
    tokenDelayMs: 60000*5
    savedToken: token
    lowWaterMark: 5
    highWaterMark: 20
    onError:onWatchError
    updateProcessStatusFn: updateProcessStatusForWatch
    updateTokenTimerS: 120 # seconds
  }
  watchHelper.watchTarget opt, (err, retObject)->
    if err
      debug.critical 'watchProp error',err
      return gracefulExit err
    gWatchedObject = retObject if retObject

# get token and watch
getTokenAndWatch=()->
  watchHelper.getToken PropertiesCol,(err, {token,tokenClusterTs})->
    return gracefulExit err if err
    try
      await onTokenUpdate({token,tokenClusterTs})
    catch err
      debug.error err
      return gracefulExit err
    watchProp(token,tokenClusterTs)

main = ()->
  isForce = AVGS.indexOf('force')>=0
  try
    # check running process
    hasRunningProcess = await checkRunningProcess()
    if (not isForce) and hasRunningProcess
      return gracefulExit HAS_RUNNING_PROCESS
    startTs = new Date()
    await updateProcessStatusForWatch(startTs)
    
    # get last resume token
    ret = await SysdataCol.findOne {_id:PROCESS_STATUS_ID}
    debug.verbose "sysData of #{PROCESS_STATUS_ID}", ret

    if (token = ret?.token) \
    and (ret?.resumeMt or (new Date(ret.tokenClusterTs) > (startTs - HALF_DAY_IN_MS)))
      try
        token = JSON.parse(token)
      catch e # if no token, do init, query from last 12 hours.
        debug.warning 'invalid token, do init',token
        return getTokenAndWatch()
      debug.info 'MSG: do watch from ', ret.tokenClusterTs
      watchProp(token,ret.tokenClusterTs,ret?.resumeMt)
    else # get token
      debug.info 'MSG: do init, getTokenAndWatch'
      getTokenAndWatch()
  catch error
    debug.error error
    return gracefulExit error

main()


