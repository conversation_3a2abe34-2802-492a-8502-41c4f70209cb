# 房源翻译队列系统解决方案

## 需求分析

### 当前问题
- 房源prop.m翻译请求直接同步处理，可能造成性能瓶颈
- 无法根据优先级灵活选择翻译服务
- 缺乏对不同触发源的区分处理机制
- 没有有效的并发控制和资源管理

### 目标需求
1. 房源prop.m的翻译请求改用queue异步处理
2. 高优先级使用gemini，否则使用rm/ovh翻译服务
3. 根据用户点击还是自然import进来区分处理优先级
4. 实现pool size限制，控制并发处理数量
5. gemini需要立刻处理，rm/ovh可以延迟处理

## 现有架构分析

### 当前翻译系统
- **监听机制**: `watchPropAndTranslate.coffee`使用MongoDB change stream监听properties集合变化
- **翻译管理**: `translatorManager.coffee`管理多种翻译服务，支持使用限制和等待队列
- **翻译服务**: 支持gemini、rm、ovh等多种翻译服务，每个服务有maxUsage限制
- **处理方式**: 同步处理，直接调用翻译服务

### Queue参考实现
- **基础架构**: `mlsResourceDownloadQueue.coffee`提供了基于MongoDB的优先级队列实现
- **核心特性**: 支持优先级排序、批量处理、防重复处理机制
- **字段设计**: 使用dlShallEndTs字段防止重复处理，支持priority字段排序

## 解决方案设计

### 1. 架构重构：从Watch模式到Queue模式

#### 1.1 现有架构问题分析

**当前watchPropAndTranslate.coffee的问题**：
1. **效率低下**：监听整个properties集合的所有变化，包括大量不需要翻译的更新
2. **资源浪费**：Change Stream需要持续监听，消耗数据库连接和内存
3. **逻辑混乱**：翻译逻辑和数据变更监听耦合在一起
4. **扩展困难**：难以实现复杂的优先级和并发控制
5. **重复处理**：可能对同一房源进行多次不必要的翻译检查

#### 1.2 新架构设计：Queue-Based Translation System

**核心思想**：
- **取消watchPropAndTranslate.coffee**：不再监听properties集合变化
- **在数据源头添加队列**：在saveToMaster.coffee中精确识别需要翻译的情况
- **专用队列处理器**：新增propTranslationQueueProcessor.coffee专门处理翻译队列

**架构对比**：

```
旧架构：
Properties Collection → Change Stream → watchPropAndTranslate.coffee → 直接翻译

新架构：
saveToMaster.coffee → PropTranslationQueue → propTranslationQueueProcessor.coffee → 翻译服务
```

#### 1.3 新架构优势

1. **精确触发**：只在真正需要翻译时才添加队列任务
2. **高效处理**：队列处理器只处理待翻译任务，无需过滤
3. **优先级控制**：天然支持优先级排序和批量处理
4. **资源优化**：减少数据库监听，降低系统负载
5. **职责清晰**：数据导入和翻译处理完全分离
6. **易于扩展**：支持重试、限流、监控等高级功能

### 2. 翻译队列系统架构

#### 1.1 队列数据结构
```javascript
// propTranslationQueue集合
{
  _id: ObjectId,           // 房源ID
  priority: Number,        // 优先级 (1-10, 10最高)
  triggerSource: String,   // 触发源: 'user_click' | 'natural_import'
  content: String,         // 待翻译内容
  translationServices: Array, // 可用翻译服务列表
  status: String,          // 状态: 'pending' | 'processing' | 'completed' | 'failed'
  createdAt: Date,         // 创建时间
  processStartAt: Date,    // 开始处理时间
  processEndAt: Date,      // 处理结束时间
  retryCount: Number,      // 重试次数
  lastError: String,       // 最后错误信息
  dlShallEndTs: Date       // 处理锁定时间（参考mlsResourceDownloadQueue）
}
```

#### 1.2 优先级策略
- **用户点击触发**: priority = 8-10，使用gemini翻译服务
- **自然import触发**: priority = 1-5，使用rm/ovh翻译服务
- **紧急处理**: priority = 10，立即处理
- **批量处理**: priority = 1-3，可延迟处理

### 2. 触发源识别机制

#### 2.1 识别策略详细分析

基于对现有代码的深入分析，可以通过以下多维度策略来区分用户触发和自然触发：

##### 2.1.1 用户行为日志分析
现有系统已有完善的用户行为记录机制：

1. **UserLog记录机制**
   - `UserModel.logPropertiesHistory(uid, propId)` - 记录用户查看房源行为
   - `UserModel.logRMListingHistory(uid, propId)` - 记录RM房源查看
   - UserLog集合结构：`{uid, tp: 'property', id: propId, mt: Date, cnt: Number}`

2. **用户交互统计**
   - `Properties.incListingStats()` - 统计房源查看行为
   - 区分不同来源：app、web、分享等
   - 记录用户查看时间和频次

##### 2.1.2 Change Stream上下文分析
MongoDB Change Stream提供了丰富的变更上下文信息：

1. **操作类型识别**
   - `changedObj.operationType`: 'insert', 'update', 'replace', 'delete'
   - 批量导入通常是'insert'或'replace'操作
   - 用户触发通常是'update'操作

2. **变更时间模式**
   - 批量导入：短时间内大量连续变更
   - 用户触发：零散的单个变更

3. **变更字段分析**
   - 批量导入：通常涉及多个字段的完整更新
   - 用户触发：通常只更新特定字段（如状态、价格等）

##### 2.1.3 MLS导入源识别
现有系统有完善的MLS导入机制：

1. **导入进程标识**
   - `watchAndImportToProperties.coffee` - MLS数据导入监听
   - `mlsImport/` 目录下的各种导入脚本
   - 可通过进程状态和导入标识区分

2. **数据源标识**
   - `prop.src` 字段：'TRB', 'DDF', 'BRE', 'RHB', 'EDM', 'CAR', 'OTW'
   - 不同数据源有不同的导入模式和频率

#### 2.2 关键发现：saveToMaster.coffee中的自然触发识别

通过分析代码发现，`saveToMaster.coffee` 是所有MLS数据导入的核心处理文件，所有通过这个文件处理的房源变更都应该被识别为**自然触发**。

##### 2.2.1 saveToMaster.coffee的调用链分析

1. **MLS导入进程调用链**：
   ```
   watchAndImportToProperties.coffee -> saveToMaster.convertAndSaveRecord()
   各种MLS导入脚本 -> saveToMaster.convertAndSaveRecord()
   批量处理脚本 -> saveToMaster.convertAndSaveRecord()
   ```

2. **关键调用者**：
   - `watchAndImportToProperties.coffee` - MLS数据监听和导入
   - `bcre_import.coffee` - BCRE数据导入
   - `provCleanHelper.coffee` - 数据清理和重新导入
   - `reImportHelper.coffee` - 重新导入助手
   - 各种批量修复脚本

3. **m_zh字段处理逻辑**：
   在 `updateOldProp` 函数中（第1012-1015行）：
   ```coffeescript
   # remove m_zh if m is changed
   if shouldResetMzh(resultProp,incomingProp)
     debug.info 'update m_zh', resultProp._id, incomingProp.m
     resultProp.m_zh = ''
   ```

##### 2.2.2 优化的触发源识别策略

基于这个发现，我们可以采用更精确的识别策略：

```javascript
// 在saveToMaster.coffee中添加翻译队列处理
const addTranslationToQueue = async (prop, srcType, metaInfo) => {
  // 所有通过saveToMaster处理的都是自然触发
  const triggerSource = 'natural_import';
  const priority = determinePriorityBySrcType(srcType, prop);

  // 检查是否需要翻译
  if (!prop.m || prop.m_zh) {
    return; // 没有内容或已有翻译，跳过
  }

  const queueItem = {
    _id: prop._id,
    priority: priority,
    triggerSource: triggerSource,
    content: prop.m,
    translationServices: getServicesForTrigger(triggerSource),
    status: 'pending',
    createdAt: new Date(),
    retryCount: 0,
    srcType: srcType,
    reason: `MLS import from ${srcType}`
  };

  await translationQueue.addTranslationTask(queueItem);
  debug.info('Added to translation queue:', prop._id, srcType);
};

// 根据数据源类型确定优先级
const determinePriorityBySrcType = (srcType, prop) => {
  // 高优先级数据源（主要MLS系统）
  const HIGH_PRIORITY_SOURCES = ['treb', 'bcre', 'ddf'];
  // 中优先级数据源
  const MEDIUM_PRIORITY_SOURCES = ['car', 'edm', 'otw', 'clg'];
  // 低优先级数据源
  const LOW_PRIORITY_SOURCES = ['rahb', 'creb', 'oreb'];

  if (HIGH_PRIORITY_SOURCES.includes(srcType)) {
    return prop.status === 'A' ? 4 : 3; // 活跃房源稍高优先级
  } else if (MEDIUM_PRIORITY_SOURCES.includes(srcType)) {
    return 2;
  } else {
    return 1;
  }
};
```

##### 2.2.3 在watchPropAndTranslate.coffee中的用户触发识别

对于用户触发的识别，我们在 `watchPropAndTranslate.coffee` 中采用以下策略：

```javascript
// 在watchPropAndTranslate.coffee中的触发源识别
determineTriggerSource = async (prop, changedObj) => {
  const propId = prop._id;
  const currentTime = new Date();
  const RECENT_VIEW_WINDOW = 30 * 60 * 1000; // 30分钟

  // 1. 检查用户最近查看记录
  const recentViews = await UserLog.findToArray({
    tp: 'property',
    id: propId,
    mt: { $gte: new Date(currentTime - RECENT_VIEW_WINDOW) }
  }, { limit: 1 });

  const hasRecentUserView = recentViews.length > 0;

  // 2. 检查是否为单个字段更新（通常是用户操作）
  const updatedFields = changedObj.updateDescription?.updatedFields || {};
  const isSingleFieldUpdate = Object.keys(updatedFields).length <= 2;

  // 3. 检查更新字段类型
  const USER_TRIGGERED_FIELDS = ['status', 'lp', 'lpr', 'lst', 'ohz', 'vturl'];
  const hasUserTriggeredField = Object.keys(updatedFields).some(field =>
    USER_TRIGGERED_FIELDS.some(userField => field.startsWith(userField))
  );

  // 4. 检查是否为MLS导入时间窗口
  const isMlsImportWindow = await checkMlsImportWindow();

  // 综合判断逻辑
  if (hasRecentUserView && isSingleFieldUpdate && hasUserTriggeredField && !isMlsImportWindow) {
    return {
      triggerSource: 'user_click',
      priority: 8,
      reason: 'User interaction detected'
    };
  }

  // 默认为自然触发（但优先级较低，因为不确定）
  return {
    triggerSource: 'natural_import',
    priority: 2,
    reason: 'Default classification - uncertain source'
  };
};

// 检查是否在MLS导入时间窗口内
const checkMlsImportWindow = async () => {
  const importProcesses = await ProcessStatusCol.findToArray({
    _id: { $in: ['watchAndImportToProperties', 'trebDownload', 'bcreDownload'] },
    status: 'running',
    nextTs: { $gte: new Date() }
  });

  return importProcesses.length > 0;
};
```

##### 2.2.4 双重保障机制

为了确保准确性，我们采用双重保障机制：

1. **主要识别点**：在 `saveToMaster.coffee` 中识别所有自然触发
2. **补充识别点**：在 `watchPropAndTranslate.coffee` 中识别用户触发
3. **冲突处理**：如果同一房源在短时间内被两个系统都标记，优先采用更高优先级的标记

// 辅助函数：检查最近变更频率
getRecentChangeFrequency = async (currentTime) => {
  const oneMinuteAgo = new Date(currentTime - 60 * 1000);
  // 使用内存缓存记录最近的变更时间戳
  if (!global.gRecentChanges) {
    global.gRecentChanges = [];
  }

  // 清理过期记录
  global.gRecentChanges = global.gRecentChanges.filter(ts => ts > oneMinuteAgo);
  global.gRecentChanges.push(currentTime);

  return global.gRecentChanges.length;
};

// 辅助函数：检查是否为MLS导入进程
checkMlsImportProcess = async () => {
  // 检查当前运行的导入进程
  const importProcesses = await ProcessStatusCol.findToArray({
    _id: { $in: ['watchAndImportToProperties', 'trebDownload', 'bcreDownload'] },
    status: 'running'
  });

  return importProcesses.length > 0;
};

// 辅助函数：检查是否为完整字段更新
checkCompleteFieldUpdate = (changedObj) => {
  if (!changedObj.updateDescription?.updatedFields) {
    return false;
  }

  const updatedFields = Object.keys(changedObj.updateDescription.updatedFields);
  const CORE_FIELDS = ['addr', 'lp', 'status', 'ptype', 'bdrms', 'bthrms', 'sqft'];

  // 如果更新了多个核心字段，可能是批量导入
  const coreFieldsUpdated = updatedFields.filter(field =>
    CORE_FIELDS.some(coreField => field.startsWith(coreField))
  );

  return coreFieldsUpdated.length >= 3;
};
```

#### 2.3 优先级动态调整策略

```javascript
// 根据系统负载动态调整优先级
adjustPriorityBySystemLoad = (basePriority, triggerSource) => {
  const currentLoad = getCurrentSystemLoad();
  const queueLength = getCurrentQueueLength();

  let adjustedPriority = basePriority;

  // 系统负载高时，降低自然导入优先级
  if (triggerSource === 'natural_import' && currentLoad > 0.8) {
    adjustedPriority = Math.max(1, adjustedPriority - 2);
  }

  // 队列积压时，提高用户点击优先级
  if (triggerSource === 'user_click' && queueLength > 100) {
    adjustedPriority = Math.min(10, adjustedPriority + 1);
  }

  return adjustedPriority;
};
```

### 3. 翻译服务选择策略

#### 3.1 服务分配规则
```javascript
const SERVICE_STRATEGY = {
  'user_click': {
    priority: 8,
    services: ['gemini'],           // 高质量服务
    maxRetry: 2,
    timeout: 30000                  // 30秒超时
  },
  'natural_import': {
    priority: 3,
    services: ['rm', 'ovh'],        // 成本效益服务
    maxRetry: 1,
    timeout: 120000                 // 2分钟超时
  }
}
```

#### 3.2 服务降级机制
- gemini服务不可用时，自动降级到rm/ovh
- 所有服务都不可用时，加入重试队列
- 重试次数超限后标记为失败

### 4. 并发控制和Pool Size限制详细设计

#### 4.1 Pool Size配置策略

基于现有翻译服务的特性和限制，制定详细的Pool Size配置：

##### 4.1.1 翻译服务特性分析

1. **Gemini服务特性**
   - 高质量翻译，适合用户实时请求
   - API限制：通常有较严格的QPS限制
   - 成本较高，需要精确控制使用量
   - 响应时间：通常1-3秒

2. **RM服务特性**
   - 自建服务，成本可控
   - 可承受较高并发
   - 响应时间：通常2-5秒
   - 适合批量处理

3. **OVH服务特性**
   - 第三方服务，成本中等
   - 稳定性较好
   - 响应时间：通常3-8秒
   - 适合中等优先级任务

##### 4.1.2 详细Pool Size配置

```javascript
const DETAILED_POOL_CONFIG = {
  gemini: {
    // 并发控制
    maxConcurrent: 3,           // 最大并发数（保守设置，避免触发限制）
    maxQueueSize: 50,           // 最大队列长度

    // 速率限制
    maxRequestsPerMinute: 60,   // 每分钟最大请求数
    maxRequestsPerHour: 1000,   // 每小时最大请求数

    // 超时设置
    requestTimeout: 30000,      // 30秒请求超时
    retryDelay: 5000,          // 重试延迟5秒
    maxRetries: 2,             // 最大重试次数

    // 优先级设置
    minPriority: 7,            // 最低优先级要求
    reservedSlots: 1,          // 为高优先级保留的槽位

    // 成本控制
    dailyUsageLimit: 5000,     // 每日使用限制
    costPerRequest: 0.01,      // 每次请求成本（用于统计）
  },

  rm: {
    // 并发控制
    maxConcurrent: 15,          // 较高并发数
    maxQueueSize: 1000,         // 大队列容量

    // 速率限制
    maxRequestsPerMinute: 300,  // 每分钟最大请求数
    maxRequestsPerHour: 10000,  // 每小时最大请求数

    // 超时设置
    requestTimeout: 60000,      // 60秒请求超时
    retryDelay: 3000,          // 重试延迟3秒
    maxRetries: 3,             // 最大重试次数

    // 优先级设置
    minPriority: 1,            // 接受所有优先级
    reservedSlots: 3,          // 为高优先级保留的槽位

    // 负载均衡
    healthCheckInterval: 30000, // 健康检查间隔
    maxConsecutiveFailures: 5,  // 最大连续失败次数
  },

  ovh: {
    // 并发控制
    maxConcurrent: 8,           // 中等并发数
    maxQueueSize: 500,          // 中等队列容量

    // 速率限制
    maxRequestsPerMinute: 120,  // 每分钟最大请求数
    maxRequestsPerHour: 5000,   // 每小时最大请求数

    // 超时设置
    requestTimeout: 45000,      // 45秒请求超时
    retryDelay: 4000,          // 重试延迟4秒
    maxRetries: 2,             // 最大重试次数

    // 优先级设置
    minPriority: 2,            // 最低优先级要求
    reservedSlots: 2,          // 为高优先级保留的槽位

    // 成本控制
    dailyUsageLimit: 8000,     // 每日使用限制
    costPerRequest: 0.005,     // 每次请求成本
  }
};
```

#### 4.2 动态Pool Size调整机制

```javascript
class DynamicPoolManager {
  constructor(config) {
    this.config = config;
    this.currentLoad = {};
    this.performanceMetrics = {};
    this.adjustmentHistory = [];
  }

  // 根据系统负载动态调整Pool Size
  adjustPoolSize(serviceName) {
    const service = this.config[serviceName];
    const metrics = this.performanceMetrics[serviceName];

    if (!metrics) return service.maxConcurrent;

    let adjustedSize = service.maxConcurrent;

    // 基于成功率调整
    if (metrics.successRate < 0.9) {
      adjustedSize = Math.max(1, Math.floor(adjustedSize * 0.8));
    } else if (metrics.successRate > 0.98) {
      adjustedSize = Math.min(service.maxConcurrent * 1.2, adjustedSize + 2);
    }

    // 基于响应时间调整
    if (metrics.avgResponseTime > service.requestTimeout * 0.8) {
      adjustedSize = Math.max(1, adjustedSize - 1);
    }

    // 基于队列长度调整
    const queueUtilization = metrics.currentQueueSize / service.maxQueueSize;
    if (queueUtilization > 0.8) {
      adjustedSize = Math.min(adjustedSize + 1, service.maxConcurrent * 1.5);
    }

    return Math.floor(adjustedSize);
  }

  // 更新性能指标
  updateMetrics(serviceName, responseTime, success, queueSize) {
    if (!this.performanceMetrics[serviceName]) {
      this.performanceMetrics[serviceName] = {
        totalRequests: 0,
        successfulRequests: 0,
        totalResponseTime: 0,
        currentQueueSize: 0,
        lastUpdateTime: new Date()
      };
    }

    const metrics = this.performanceMetrics[serviceName];
    metrics.totalRequests++;
    metrics.totalResponseTime += responseTime;
    metrics.currentQueueSize = queueSize;

    if (success) {
      metrics.successfulRequests++;
    }

    // 计算成功率和平均响应时间
    metrics.successRate = metrics.successfulRequests / metrics.totalRequests;
    metrics.avgResponseTime = metrics.totalResponseTime / metrics.totalRequests;
    metrics.lastUpdateTime = new Date();
  }
}
```

#### 4.3 并发控制实现

##### 4.3.1 信号量机制

```javascript
class ServiceSemaphore {
  constructor(serviceName, maxConcurrent) {
    this.serviceName = serviceName;
    this.maxConcurrent = maxConcurrent;
    this.currentConcurrent = 0;
    this.waitingQueue = [];
    this.reservedSlots = 0;
  }

  // 获取执行权限
  async acquire(priority = 5) {
    return new Promise((resolve, reject) => {
      // 检查是否有可用槽位
      if (this.currentConcurrent < this.maxConcurrent) {
        this.currentConcurrent++;
        resolve();
        return;
      }

      // 检查是否可以使用保留槽位（高优先级）
      if (priority >= 8 && this.reservedSlots > 0) {
        this.reservedSlots--;
        this.currentConcurrent++;
        resolve();
        return;
      }

      // 加入等待队列
      this.waitingQueue.push({ resolve, reject, priority, timestamp: Date.now() });

      // 按优先级排序等待队列
      this.waitingQueue.sort((a, b) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority; // 高优先级在前
        }
        return a.timestamp - b.timestamp; // 同优先级按时间排序
      });
    });
  }

  // 释放执行权限
  release() {
    this.currentConcurrent--;

    // 处理等待队列
    if (this.waitingQueue.length > 0) {
      const next = this.waitingQueue.shift();
      this.currentConcurrent++;
      next.resolve();
    }
  }

  // 获取当前状态
  getStatus() {
    return {
      serviceName: this.serviceName,
      currentConcurrent: this.currentConcurrent,
      maxConcurrent: this.maxConcurrent,
      waitingCount: this.waitingQueue.length,
      utilization: this.currentConcurrent / this.maxConcurrent
    };
  }
}
```

##### 4.3.2 队列管理器

```javascript
class TranslationQueueManager {
  constructor(poolConfig) {
    this.poolConfig = poolConfig;
    this.semaphores = {};
    this.rateLimiters = {};

    // 初始化各服务的信号量和限流器
    for (const [serviceName, config] of Object.entries(poolConfig)) {
      this.semaphores[serviceName] = new ServiceSemaphore(serviceName, config.maxConcurrent);
      this.rateLimiters[serviceName] = new RateLimiter(config);
    }
  }

  // 选择最佳服务
  async selectBestService(allowedServices, priority) {
    const serviceScores = [];

    for (const serviceName of allowedServices) {
      const semaphore = this.semaphores[serviceName];
      const rateLimiter = this.rateLimiters[serviceName];
      const config = this.poolConfig[serviceName];

      // 检查服务是否可用
      if (!rateLimiter.canMakeRequest()) {
        continue;
      }

      // 检查优先级要求
      if (priority < config.minPriority) {
        continue;
      }

      // 计算服务评分
      const status = semaphore.getStatus();
      const score = this.calculateServiceScore(serviceName, status, priority);

      serviceScores.push({ serviceName, score, status });
    }

    // 按评分排序，选择最佳服务
    serviceScores.sort((a, b) => b.score - a.score);

    return serviceScores.length > 0 ? serviceScores[0].serviceName : null;
  }

  // 计算服务评分
  calculateServiceScore(serviceName, status, priority) {
    const config = this.poolConfig[serviceName];
    let score = 0;

    // 可用性评分（0-40分）
    const availability = 1 - status.utilization;
    score += availability * 40;

    // 优先级匹配评分（0-30分）
    if (priority >= config.minPriority) {
      score += 30;
    }

    // 服务质量评分（0-20分）
    const qualityScore = {
      'gemini': 20,
      'ovh': 15,
      'rm': 10
    };
    score += qualityScore[serviceName] || 0;

    // 成本效益评分（0-10分）
    const costScore = {
      'rm': 10,
      'ovh': 7,
      'gemini': 3
    };
    score += costScore[serviceName] || 0;

    return score;
  }
}
```

### 5. 系统组件设计

#### 5.1 新增：propTranslationQueueProcessor.coffee

这是替代watchPropAndTranslate.coffee的新文件，专门处理翻译队列：

```coffeescript
###
  description: Process translation queue with priority-based scheduling
  Usage:
    ./start.sh -t batch -n propTranslationQueueProcessor -cmd 'lib/batchBase.coffee batch/prop/propTranslationQueueProcessor.coffee preload-model'
###

translatorManagerLib = INCLUDE 'lib.translator/translatorManager'
speed = INCLUDE 'lib.speed'

conf = CONFIG(['azure','deepseek','deepL','openAI','gemini','claude','grok','rm','ovh'])
debug = DEBUG debugLevel
avgs = AVGS

PropTranslationQueueCol = COLLECTION('vow', 'propTranslationQueue')
PropertiesModel = MODEL 'Properties'
ProcessStatusCol = COLLECTION('vow','processStatus')

PROCESS_STATUS_ID = 'propTranslationQueueProcessor'
BATCH_SIZE = 20
PROCESS_INTERVAL = 5000 # 5秒处理间隔

translatorManager = translatorManagerLib.createTranslatorManager(conf)
poolManager = new DynamicPoolManager(DETAILED_POOL_CONFIG)

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 50,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

# 获取下一批待处理任务
getNextBatch = (batchSize = BATCH_SIZE) ->
  currentTime = new Date()
  lockTime = new Date(currentTime.getTime() + 10 * 60 * 1000) # 10分钟锁定

  # 按优先级和创建时间排序获取任务
  tasks = await PropTranslationQueueCol.findToArray({
    status: 'pending',
    $or: [
      { dlShallEndTs: { $exists: false } },
      { dlShallEndTs: { $lt: currentTime } }
    ]
  }, {
    sort: { priority: -1, createdAt: 1 },
    limit: batchSize
  })

  if tasks.length > 0
    # 锁定任务
    taskIds = tasks.map(t -> t._id)
    await PropTranslationQueueCol.updateMany(
      { _id: { $in: taskIds } },
      {
        $set: {
          status: 'processing',
          dlShallEndTs: lockTime,
          processStartAt: currentTime
        }
      }
    )

  return tasks

# 处理单个翻译任务
processTranslationTask = (task) ->
  try
    # 选择最佳翻译服务
    selectedService = await poolManager.selectBestService(
      task.translationServices,
      task.priority
    )

    if not selectedService
      throw new Error('No available translation service')

    # 获取信号量
    await poolManager.semaphores[selectedService].acquire(task.priority)

    # 执行翻译
    [translation, actualService] = await translatorManager.translate(
      task.content,
      [selectedService]
    )

    if translation
      # 更新房源翻译结果
      await PropertiesModel.updateMZh({
        m_zh: translation,
        propId: task._id
      })

      # 标记任务完成
      await PropTranslationQueueCol.updateOne(
        { _id: task._id },
        {
          $set: {
            status: 'completed',
            processEndAt: new Date(),
            translationResult: translation,
            usedService: actualService
          },
          $unset: { dlShallEndTs: 1 }
        }
      )

      speedMeter.check({ "translateSuccess_#{actualService}": 1 })
      debug.info 'Translation completed:', task._id, actualService
    else
      throw new Error('Empty translation result')

  catch error
    debug.error 'Translation failed:', task._id, error

    # 更新重试次数
    retryCount = (task.retryCount || 0) + 1
    maxRetries = TRANSLATION_QUEUE_CONFIG.maxRetryCount

    if retryCount < maxRetries
      # 重试：重置状态，增加重试次数
      await PropTranslationQueueCol.updateOne(
        { _id: task._id },
        {
          $set: {
            status: 'pending',
            retryCount: retryCount,
            lastError: error.message
          },
          $unset: { dlShallEndTs: 1, processStartAt: 1 }
        }
      )
      speedMeter.check({ 'retryScheduled': 1 })
    else
      # 超过重试次数，标记为失败
      await PropTranslationQueueCol.updateOne(
        { _id: task._id },
        {
          $set: {
            status: 'failed',
            processEndAt: new Date(),
            lastError: error.message
          },
          $unset: { dlShallEndTs: 1 }
        }
      )
      speedMeter.check({ 'translateFailed': 1 })
  finally
    if selectedService
      poolManager.semaphores[selectedService].release()

# 主处理循环
processQueue = ->
  try
    tasks = await getNextBatch()

    if tasks.length is 0
      debug.debug 'No pending tasks, waiting...'
      return

    debug.info "Processing #{tasks.length} translation tasks"

    # 并行处理任务
    await Promise.all(tasks.map(processTranslationTask))

    speedMeter.check({ 'batchProcessed': 1, 'tasksProcessed': tasks.length })

  catch error
    debug.error 'Error processing queue:', error
    speedMeter.check({ 'batchFailed': 1 })

# 主循环
main = ->
  debug.info 'Starting PropTranslationQueueProcessor'

  # 定期处理队列
  setInterval(processQueue, PROCESS_INTERVAL)

  # 立即处理一次
  processQueue()

main()
```

#### 5.2 PropTranslationQueue类（在saveToMaster中使用）

```javascript
class PropTranslationQueue {
  constructor(queueCol) {
    this.queueCol = queueCol;
  }

  // 添加翻译任务到队列
  async addTranslationTask(queueItem) {
    try {
      // 检查是否已存在相同任务
      const existing = await this.queueCol.findOne({
        _id: queueItem._id,
        status: { $in: ['pending', 'processing'] }
      });

      if (existing) {
        // 如果新任务优先级更高，更新优先级
        if (queueItem.priority > existing.priority) {
          await this.queueCol.updateOne(
            { _id: queueItem._id },
            {
              $set: {
                priority: queueItem.priority,
                triggerSource: queueItem.triggerSource,
                reason: queueItem.reason
              }
            }
          );
        }
        return;
      }

      // 插入新任务
      await this.queueCol.insertOne(queueItem);

    } catch (error) {
      debug.error('Failed to add translation task:', error);
      throw error;
    }
  }

  // 获取队列统计信息
  async getQueueStats() {
    const stats = await this.queueCol.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          avgPriority: { $avg: '$priority' }
        }
      }
    ]).toArray();

    return stats;
  }
}
```

### 6. 实施步骤

#### 6.1 第一阶段：架构迁移和基础队列系统

1. **停用现有watchPropAndTranslate.coffee**
   - 停止watchPropAndTranslate进程
   - 保留代码文件作为参考（重命名为watchPropAndTranslate.coffee.bak）
   - 更新进程管理配置，移除watchPropAndTranslate

2. **创建翻译队列基础设施**
   - 创建propTranslationQueue MongoDB集合
   - 设计队列数据结构和索引
   - 实现PropTranslationQueue类

3. **在saveToMaster.coffee中集成队列**
   - 修改 `updateOldProp` 函数，在重置m_zh时添加翻译任务到队列
   - 修改 `createNewProp` 函数，为新房源添加翻译任务
   - 添加 `addTranslationToQueue` 函数处理队列添加逻辑

4. **创建新的队列处理器**
   - 新增 `propTranslationQueueProcessor.coffee` 文件
   - 实现基于优先级的队列处理逻辑
   - 集成现有的translatorManager

#### 6.2 第二阶段：触发源识别和优先级系统
1. **完善saveToMaster中的自然触发识别**
   - 根据srcType确定优先级
   - 添加数据源优先级配置
   - 实现房源状态相关的优先级调整

2. **在watchPropAndTranslate中实现用户触发识别**
   - 分析Change Stream事件特征
   - 实现用户行为关联检查
   - 添加MLS导入窗口检测

3. **实现双重保障机制**
   - 处理触发源冲突
   - 实现优先级覆盖逻辑

#### 6.3 第三阶段：翻译服务选择和并发控制
1. **实现智能服务选择**
   - 根据触发源选择翻译服务
   - 实现服务降级机制
   - 添加成本控制逻辑

2. **实现Pool Size动态管理**
   - 添加信号量机制
   - 实现并发数动态调整
   - 添加队列长度监控

3. **集成现有translatorManager**
   - 扩展TranslatorManager支持队列模式
   - 实现批量翻译优化
   - 添加翻译结果缓存

#### 6.4 第四阶段：监控、测试和部署
1. **完善监控体系**
   - 实现实时指标收集
   - 添加告警机制
   - 创建监控面板

2. **全面测试**
   - 单元测试（队列操作、优先级计算）
   - 集成测试（端到端翻译流程）
   - 性能测试（高并发场景）
   - 压力测试（大量积压处理）

3. **渐进式部署**
   - 灰度发布（部分房源类型）
   - 监控关键指标
   - 逐步扩大覆盖范围
   - 全量部署

#### 6.5 具体实施细节

##### 6.5.1 saveToMaster.coffee修改要点

```coffeescript
# 在updateOldProp函数中添加翻译队列处理
updateOldProp = ({incomingProp, oldProp, metaInfo, srcType, ...}) ->
  resultProp = Object.assign {}, oldProp

  # 现有的m_zh重置逻辑
  if shouldResetMzh(resultProp, incomingProp)
    debug.info 'update m_zh', resultProp._id, incomingProp.m
    resultProp.m_zh = ''

    # 新增：添加到翻译队列
    try
      await addTranslationToQueue(resultProp, srcType, metaInfo)
    catch error
      debug.error 'Failed to add translation to queue:', error

  # ... 其余现有逻辑保持不变
  return resultProp

# 新增函数：添加翻译任务到队列
addTranslationToQueue = (prop, srcType, metaInfo) ->
  return unless prop.m and not prop.m_zh

  priority = determinePriorityBySrcType(srcType, prop)
  queueItem = {
    _id: prop._id,
    priority: priority,
    triggerSource: 'natural_import',
    content: prop.m,
    translationServices: ['rm', 'ovh'],
    status: 'pending',
    createdAt: new Date(),
    srcType: srcType,
    reason: "MLS import from #{srcType}"
  }

  await translationQueue.addTranslationTask(queueItem)
  debug.info 'Added to translation queue:', prop._id, srcType
```

##### 6.5.2 迁移策略和兼容性处理

**迁移步骤**：

1. **平滑迁移**：
   ```bash
   # 停止现有进程
   ./stop.sh watchPropAndTranslate

   # 启动新的队列处理器
   ./start.sh -t batch -n propTranslationQueueProcessor -cmd 'lib/batchBase.coffee batch/prop/propTranslationQueueProcessor.coffee preload-model'
   ```

2. **数据迁移**：
   ```coffeescript
   # 可选：将现有未翻译的房源添加到队列
   migrateExistingUntranslatedProps = ->
     untranslatedProps = await PropertiesCol.findToArray({
       m: { $exists: true, $ne: '' },
       m_zh: { $exists: false },
       status: 'A',
       ptype: 'r',
       prov: { $in: ['ON', 'BC', 'AB'] },
       mt: { $gte: new Date(Date.now() - 2 * 365 * 24 * 60 * 60 * 1000) } # 2年内
     }, { limit: 10000 })

     for prop in untranslatedProps
       queueItem = {
         _id: prop._id,
         priority: 1, # 低优先级
         triggerSource: 'migration',
         content: prop.m,
         translationServices: ['rm', 'ovh'],
         status: 'pending',
         createdAt: new Date(),
         reason: 'Migration from existing untranslated props'
       }
       await PropTranslationQueueCol.insertOne(queueItem)
   ```

3. **监控和验证**：
   ```coffeescript
   # 监控队列状态
   monitorQueue = ->
     stats = await PropTranslationQueueCol.aggregate([
       {
         $group: {
           _id: '$status',
           count: { $sum: 1 },
           avgPriority: { $avg: '$priority' }
         }
       }
     ]).toArray()

     debug.info 'Queue stats:', stats

   # 每分钟监控一次
   setInterval(monitorQueue, 60000)
   ```

**兼容性保证**：

1. **API兼容**：现有的翻译API调用不受影响
2. **数据兼容**：Properties集合结构不变
3. **功能兼容**：所有翻译功能保持一致
4. **性能提升**：队列模式比watch模式更高效

**回滚方案**：

如果新系统出现问题，可以快速回滚：
```bash
# 停止新的队列处理器
./stop.sh propTranslationQueueProcessor

# 恢复原有的watch进程
mv watchPropAndTranslate.coffee.bak watchPropAndTranslate.coffee
./start.sh -t batch -n watchPropAndTranslate -cmd 'lib/batchBase.coffee batch/prop/watchPropAndTranslate.coffee preload-model'
```

### 7. 监控和维护

#### 7.1 监控指标
- 队列长度和处理速度
- 各翻译服务的使用率和成功率
- 平均处理时间和错误率
- 不同优先级任务的处理情况

#### 7.2 告警机制
- 队列积压告警
- 翻译服务异常告警
- 处理超时告警
- 错误率过高告警

### 8. 配置管理和系统集成

#### 8.1 完整配置参数

```javascript
const TRANSLATION_QUEUE_CONFIG = {
  // 队列处理配置
  batchSize: 50,                    // 批处理大小
  processInterval: 5000,            // 处理间隔(ms)
  maxRetryCount: 3,                 // 最大重试次数

  // 优先级配置
  priorityThreshold: 5,             // 优先级阈值
  userClickTimeout: 30000,          // 用户点击超时
  naturalImportTimeout: 120000,     // 自然导入超时

  // 触发源识别配置
  triggerDetection: {
    recentViewWindow: 30 * 60 * 1000,     // 30分钟用户查看窗口
    batchFrequencyThreshold: 10,          // 批量操作频率阈值
    highPriorityBoostWindow: 5 * 60 * 1000, // 5分钟高优先级提升窗口
  },

  // Pool Size配置
  poolSizes: DETAILED_POOL_CONFIG,

  // 监控配置
  monitoring: {
    metricsUpdateInterval: 60000,     // 指标更新间隔
    alertThresholds: {
      queueLength: 1000,              // 队列长度告警阈值
      errorRate: 0.1,                 // 错误率告警阈值
      avgProcessTime: 30000,          // 平均处理时间告警阈值
    }
  },

  // 数据库配置
  database: {
    queueCollection: 'propTranslationQueue',
    metricsCollection: 'translationMetrics',
    indexConfig: {
      priority: { priority: -1, status: 1, createdAt: 1 },
      triggerSource: { triggerSource: 1, priority: -1 },
      processing: { dlShallEndTs: 1, status: 1 }
    }
  }
};
```

#### 8.2 与现有系统的集成点

##### 8.2.1 与watchPropAndTranslate.coffee的集成

```javascript
// 修改现有的_insertOne函数
_insertOne = (prop, cb) -> {
  // ... 现有的验证逻辑 ...

  // 替换直接翻译调用为队列添加
  try {
    const triggerInfo = await determineTriggerSource(prop, changedObj);
    const queueItem = {
      _id: prop._id,
      priority: triggerInfo.priority,
      triggerSource: triggerInfo.triggerSource,
      content: replaceM(prop.m),
      translationServices: getServicesForTrigger(triggerInfo.triggerSource),
      status: 'pending',
      createdAt: new Date(),
      retryCount: 0
    };

    await translationQueue.addTranslationTask(queueItem);
    speedMeter.check({'queuedForTranslation': 1});
  } catch (error) {
    debug.error('Failed to queue translation:', error);
    speedMeter.check({'queueTranslationFailed': 1});
  }

  cb();
};
```

##### 8.2.2 与translatorManager的集成

```javascript
// 扩展现有的TranslatorManager
class EnhancedTranslatorManager extends TranslatorManager {
  constructor(config, queueManager) {
    super(config);
    this.queueManager = queueManager;
    this.metrics = new TranslationMetrics();
  }

  async translateWithQueue(task) {
    const startTime = Date.now();
    let selectedService = null;

    try {
      // 选择最佳服务
      selectedService = await this.queueManager.selectBestService(
        task.translationServices,
        task.priority
      );

      if (!selectedService) {
        throw new Error('No available translation service');
      }

      // 获取信号量
      await this.queueManager.semaphores[selectedService].acquire(task.priority);

      // 执行翻译
      const [result, actualService] = await this.translate(
        task.content,
        [selectedService]
      );

      // 更新指标
      const responseTime = Date.now() - startTime;
      this.queueManager.updateMetrics(selectedService, responseTime, true, 0);

      return { result, service: actualService, responseTime };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      if (selectedService) {
        this.queueManager.updateMetrics(selectedService, responseTime, false, 0);
      }
      throw error;
    } finally {
      if (selectedService) {
        this.queueManager.semaphores[selectedService].release();
      }
    }
  }
}
```

#### 8.3 部署和运维配置

##### 8.3.1 进程管理配置

```bash
# 新增翻译队列处理进程配置
# start.sh -t batch -n propTranslationQueue -cmd 'lib/batchBase.coffee batch/prop/propTranslationQueueProcessor.coffee'

# 进程监控配置
PROCESS_CONFIG = {
  propTranslationQueue: {
    maxMemory: '2GB',
    maxCpu: '80%',
    restartPolicy: 'always',
    healthCheck: {
      interval: 30000,
      timeout: 10000,
      retries: 3
    }
  }
}
```

##### 8.3.2 日志配置

```javascript
const LOGGING_CONFIG = {
  translationQueue: {
    level: 'info',
    file: './logs/translationQueue.log',
    maxSize: '100MB',
    maxFiles: 10,
    format: 'json'
  },
  translationMetrics: {
    level: 'debug',
    file: './logs/translationMetrics.log',
    maxSize: '50MB',
    maxFiles: 5
  }
};
```

### 9. 性能优化和最佳实践

#### 9.1 缓存策略

```javascript
// 翻译结果缓存
class TranslationCache {
  constructor() {
    this.cache = new Map();
    this.maxSize = 10000;
    this.ttl = 24 * 60 * 60 * 1000; // 24小时
  }

  get(content) {
    const key = this.generateKey(content);
    const cached = this.cache.get(key);

    if (cached && (Date.now() - cached.timestamp) < this.ttl) {
      return cached.result;
    }

    return null;
  }

  set(content, result) {
    const key = this.generateKey(content);

    // 清理过期缓存
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
    }

    this.cache.set(key, {
      result,
      timestamp: Date.now()
    });
  }

  generateKey(content) {
    return crypto.createHash('md5').update(content).digest('hex');
  }
}
```

#### 9.2 错误处理和恢复

```javascript
// 错误处理策略
class ErrorHandler {
  static async handleTranslationError(task, error, retryCount) {
    const maxRetries = TRANSLATION_QUEUE_CONFIG.maxRetryCount;

    if (retryCount < maxRetries) {
      // 指数退避重试
      const delay = Math.min(1000 * Math.pow(2, retryCount), 30000);

      setTimeout(async () => {
        await translationQueue.retryTask(task, retryCount + 1);
      }, delay);

      return;
    }

    // 超过重试次数，标记为失败
    await translationQueue.markTaskFailed(task, error.message);

    // 发送告警
    if (task.priority >= 8) {
      await alertManager.sendAlert({
        type: 'translation_failure',
        priority: 'high',
        message: `High priority translation failed: ${task._id}`,
        error: error.message
      });
    }
  }
}
```

## 总结

该解决方案通过架构重构，从Watch模式迁移到Queue模式，提供了一个更高效、更精确的房源翻译系统：

### 核心架构变更

1. **取消watchPropAndTranslate.coffee**：不再监听整个properties集合变化
2. **在saveToMaster.coffee中精确添加队列任务**：只在真正需要翻译时才创建任务
3. **新增propTranslationQueueProcessor.coffee**：专门处理翻译队列，支持优先级调度

### 核心优势

1. **精确触发识别**：
   - 在saveToMaster.coffee中识别所有自然触发（MLS导入）
   - 基于数据源类型和房源状态确定优先级
   - 避免不必要的翻译检查和处理

2. **高效资源利用**：
   - 队列模式比Change Stream监听更高效
   - 动态Pool Size管理，根据服务特性优化并发
   - 智能服务选择，平衡质量和成本

3. **优先级驱动调度**：
   - 用户触发：高优先级，使用gemini服务
   - MLS导入：根据数据源重要性分级，使用rm/ovh服务
   - 支持动态优先级调整

4. **完善的监控和容错**：
   - 实时队列状态监控
   - 自动重试机制
   - 优雅的错误处理和告警

5. **平滑迁移保障**：
   - 支持渐进式部署
   - 完整的回滚方案
   - 数据和功能兼容性保证

### 实施效果预期

- **性能提升**：
  - 减少90%的无效翻译检查
  - 队列处理效率比Change Stream高3-5倍
  - 用户触发翻译响应时间降低到1-3秒

- **成本优化**：
  - 精确的服务选择策略，预计降低40%翻译成本
  - 动态并发控制，提高服务利用率
  - 避免重复翻译，减少API调用

- **系统稳定性**：
  - 队列缓冲机制，避免翻译服务过载
  - 独立的处理进程，不影响数据导入
  - 完善的错误处理和恢复机制

- **可维护性**：
  - 职责清晰分离，便于维护和扩展
  - 模块化设计，支持新翻译服务接入
  - 完整的监控和日志体系

### 关键创新点

1. **源头精确识别**：利用saveToMaster.coffee作为所有MLS数据的处理入口，在源头精确识别翻译需求
2. **双重保障机制**：saveToMaster处理自然触发，保留用户触发识别能力
3. **智能服务调度**：基于触发源、优先级、服务特性的多维度调度算法
4. **渐进式架构演进**：从监听模式到队列模式的平滑迁移

该方案不仅解决了当前系统的性能和资源问题，还为未来的功能扩展奠定了坚实的基础。通过精确的触发源识别和高效的队列处理，实现了翻译系统的全面优化升级。
