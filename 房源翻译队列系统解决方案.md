# 房源翻译队列系统解决方案

## 需求分析

### 当前问题
- 房源prop.m翻译请求直接同步处理，可能造成性能瓶颈
- 无法根据优先级灵活选择翻译服务
- 缺乏对不同触发源的区分处理机制
- 没有有效的并发控制和资源管理

### 目标需求
1. 房源prop.m的翻译请求改用queue异步处理
2. 高优先级使用gemini，否则使用rm/ovh翻译服务
3. 根据用户点击还是自然import进来区分处理优先级
4. 实现pool size限制，控制并发处理数量
5. gemini需要立刻处理，rm/ovh可以延迟处理

## 现有架构分析

### 当前翻译系统
- **监听机制**: `watchPropAndTranslate.coffee`使用MongoDB change stream监听properties集合变化
- **翻译管理**: `translatorManager.coffee`管理多种翻译服务，支持使用限制和等待队列
- **翻译服务**: 支持gemini、rm、ovh等多种翻译服务，每个服务有maxUsage限制
- **处理方式**: 同步处理，直接调用翻译服务

### Queue参考实现
- **基础架构**: `mlsResourceDownloadQueue.coffee`提供了基于MongoDB的优先级队列实现
- **核心特性**: 支持优先级排序、批量处理、防重复处理机制
- **字段设计**: 使用dlShallEndTs字段防止重复处理，支持priority字段排序

## 解决方案设计

### 1. 翻译队列系统架构

#### 1.1 队列数据结构
```javascript
// propTranslationQueue集合
{
  _id: ObjectId,           // 房源ID
  priority: Number,        // 优先级 (1-10, 10最高)
  triggerSource: String,   // 触发源: 'user_click' | 'natural_import'
  content: String,         // 待翻译内容
  translationServices: Array, // 可用翻译服务列表
  status: String,          // 状态: 'pending' | 'processing' | 'completed' | 'failed'
  createdAt: Date,         // 创建时间
  processStartAt: Date,    // 开始处理时间
  processEndAt: Date,      // 处理结束时间
  retryCount: Number,      // 重试次数
  lastError: String,       // 最后错误信息
  dlShallEndTs: Date       // 处理锁定时间（参考mlsResourceDownloadQueue）
}
```

#### 1.2 优先级策略
- **用户点击触发**: priority = 8-10，使用gemini翻译服务
- **自然import触发**: priority = 1-5，使用rm/ovh翻译服务
- **紧急处理**: priority = 10，立即处理
- **批量处理**: priority = 1-3，可延迟处理

### 2. 触发源识别机制

#### 2.1 识别策略详细分析

基于对现有代码的深入分析，可以通过以下多维度策略来区分用户触发和自然触发：

##### 2.1.1 用户行为日志分析
现有系统已有完善的用户行为记录机制：

1. **UserLog记录机制**
   - `UserModel.logPropertiesHistory(uid, propId)` - 记录用户查看房源行为
   - `UserModel.logRMListingHistory(uid, propId)` - 记录RM房源查看
   - UserLog集合结构：`{uid, tp: 'property', id: propId, mt: Date, cnt: Number}`

2. **用户交互统计**
   - `Properties.incListingStats()` - 统计房源查看行为
   - 区分不同来源：app、web、分享等
   - 记录用户查看时间和频次

##### 2.1.2 Change Stream上下文分析
MongoDB Change Stream提供了丰富的变更上下文信息：

1. **操作类型识别**
   - `changedObj.operationType`: 'insert', 'update', 'replace', 'delete'
   - 批量导入通常是'insert'或'replace'操作
   - 用户触发通常是'update'操作

2. **变更时间模式**
   - 批量导入：短时间内大量连续变更
   - 用户触发：零散的单个变更

3. **变更字段分析**
   - 批量导入：通常涉及多个字段的完整更新
   - 用户触发：通常只更新特定字段（如状态、价格等）

##### 2.1.3 MLS导入源识别
现有系统有完善的MLS导入机制：

1. **导入进程标识**
   - `watchAndImportToProperties.coffee` - MLS数据导入监听
   - `mlsImport/` 目录下的各种导入脚本
   - 可通过进程状态和导入标识区分

2. **数据源标识**
   - `prop.src` 字段：'TRB', 'DDF', 'BRE', 'RHB', 'EDM', 'CAR', 'OTW'
   - 不同数据源有不同的导入模式和频率

#### 2.2 具体实现逻辑

```javascript
// 触发源识别的详细实现
determineTriggerSource = async (prop, changedObj) => {
  const propId = prop._id;
  const currentTime = new Date();
  const RECENT_VIEW_WINDOW = 30 * 60 * 1000; // 30分钟
  const BATCH_FREQUENCY_THRESHOLD = 10; // 10个/分钟

  // 1. 检查用户最近查看记录
  const recentViews = await UserLog.findToArray({
    tp: 'property',
    id: propId,
    mt: { $gte: new Date(currentTime - RECENT_VIEW_WINDOW) }
  }, { limit: 1 });

  const hasRecentUserView = recentViews.length > 0;

  // 2. 检查变更操作类型和频率
  const isBulkOperation = changedObj.operationType === 'insert' ||
                         changedObj.operationType === 'replace';

  // 3. 检查最近变更频率（滑动窗口）
  const recentChanges = await getRecentChangeFrequency(currentTime);
  const isHighFrequency = recentChanges > BATCH_FREQUENCY_THRESHOLD;

  // 4. 检查是否来自MLS导入进程
  const isMlsImport = await checkMlsImportProcess();

  // 5. 检查房源数据源和更新模式
  const isFromExternalSource = prop.src && prop.src !== 'RM';
  const hasCompleteFieldUpdate = checkCompleteFieldUpdate(changedObj);

  // 综合判断逻辑
  if (hasRecentUserView && !isBulkOperation && !isHighFrequency && !isMlsImport) {
    return {
      triggerSource: 'user_click',
      priority: 8,
      reason: 'Recent user view detected'
    };
  }

  if (isBulkOperation || isHighFrequency || isMlsImport ||
      (isFromExternalSource && hasCompleteFieldUpdate)) {
    return {
      triggerSource: 'natural_import',
      priority: 3,
      reason: 'Batch import or MLS sync detected'
    };
  }

  // 默认情况：根据时间和频率判断
  return {
    triggerSource: isHighFrequency ? 'natural_import' : 'user_click',
    priority: isHighFrequency ? 3 : 6,
    reason: 'Frequency-based classification'
  };
};

// 辅助函数：检查最近变更频率
getRecentChangeFrequency = async (currentTime) => {
  const oneMinuteAgo = new Date(currentTime - 60 * 1000);
  // 使用内存缓存记录最近的变更时间戳
  if (!global.gRecentChanges) {
    global.gRecentChanges = [];
  }

  // 清理过期记录
  global.gRecentChanges = global.gRecentChanges.filter(ts => ts > oneMinuteAgo);
  global.gRecentChanges.push(currentTime);

  return global.gRecentChanges.length;
};

// 辅助函数：检查是否为MLS导入进程
checkMlsImportProcess = async () => {
  // 检查当前运行的导入进程
  const importProcesses = await ProcessStatusCol.findToArray({
    _id: { $in: ['watchAndImportToProperties', 'trebDownload', 'bcreDownload'] },
    status: 'running'
  });

  return importProcesses.length > 0;
};

// 辅助函数：检查是否为完整字段更新
checkCompleteFieldUpdate = (changedObj) => {
  if (!changedObj.updateDescription?.updatedFields) {
    return false;
  }

  const updatedFields = Object.keys(changedObj.updateDescription.updatedFields);
  const CORE_FIELDS = ['addr', 'lp', 'status', 'ptype', 'bdrms', 'bthrms', 'sqft'];

  // 如果更新了多个核心字段，可能是批量导入
  const coreFieldsUpdated = updatedFields.filter(field =>
    CORE_FIELDS.some(coreField => field.startsWith(coreField))
  );

  return coreFieldsUpdated.length >= 3;
};
```

#### 2.3 优先级动态调整策略

```javascript
// 根据系统负载动态调整优先级
adjustPriorityBySystemLoad = (basePriority, triggerSource) => {
  const currentLoad = getCurrentSystemLoad();
  const queueLength = getCurrentQueueLength();

  let adjustedPriority = basePriority;

  // 系统负载高时，降低自然导入优先级
  if (triggerSource === 'natural_import' && currentLoad > 0.8) {
    adjustedPriority = Math.max(1, adjustedPriority - 2);
  }

  // 队列积压时，提高用户点击优先级
  if (triggerSource === 'user_click' && queueLength > 100) {
    adjustedPriority = Math.min(10, adjustedPriority + 1);
  }

  return adjustedPriority;
};
```

### 3. 翻译服务选择策略

#### 3.1 服务分配规则
```javascript
const SERVICE_STRATEGY = {
  'user_click': {
    priority: 8,
    services: ['gemini'],           // 高质量服务
    maxRetry: 2,
    timeout: 30000                  // 30秒超时
  },
  'natural_import': {
    priority: 3,
    services: ['rm', 'ovh'],        // 成本效益服务
    maxRetry: 1,
    timeout: 120000                 // 2分钟超时
  }
}
```

#### 3.2 服务降级机制
- gemini服务不可用时，自动降级到rm/ovh
- 所有服务都不可用时，加入重试队列
- 重试次数超限后标记为失败

### 4. 并发控制和Pool Size限制详细设计

#### 4.1 Pool Size配置策略

基于现有翻译服务的特性和限制，制定详细的Pool Size配置：

##### 4.1.1 翻译服务特性分析

1. **Gemini服务特性**
   - 高质量翻译，适合用户实时请求
   - API限制：通常有较严格的QPS限制
   - 成本较高，需要精确控制使用量
   - 响应时间：通常1-3秒

2. **RM服务特性**
   - 自建服务，成本可控
   - 可承受较高并发
   - 响应时间：通常2-5秒
   - 适合批量处理

3. **OVH服务特性**
   - 第三方服务，成本中等
   - 稳定性较好
   - 响应时间：通常3-8秒
   - 适合中等优先级任务

##### 4.1.2 详细Pool Size配置

```javascript
const DETAILED_POOL_CONFIG = {
  gemini: {
    // 并发控制
    maxConcurrent: 3,           // 最大并发数（保守设置，避免触发限制）
    maxQueueSize: 50,           // 最大队列长度

    // 速率限制
    maxRequestsPerMinute: 60,   // 每分钟最大请求数
    maxRequestsPerHour: 1000,   // 每小时最大请求数

    // 超时设置
    requestTimeout: 30000,      // 30秒请求超时
    retryDelay: 5000,          // 重试延迟5秒
    maxRetries: 2,             // 最大重试次数

    // 优先级设置
    minPriority: 7,            // 最低优先级要求
    reservedSlots: 1,          // 为高优先级保留的槽位

    // 成本控制
    dailyUsageLimit: 5000,     // 每日使用限制
    costPerRequest: 0.01,      // 每次请求成本（用于统计）
  },

  rm: {
    // 并发控制
    maxConcurrent: 15,          // 较高并发数
    maxQueueSize: 1000,         // 大队列容量

    // 速率限制
    maxRequestsPerMinute: 300,  // 每分钟最大请求数
    maxRequestsPerHour: 10000,  // 每小时最大请求数

    // 超时设置
    requestTimeout: 60000,      // 60秒请求超时
    retryDelay: 3000,          // 重试延迟3秒
    maxRetries: 3,             // 最大重试次数

    // 优先级设置
    minPriority: 1,            // 接受所有优先级
    reservedSlots: 3,          // 为高优先级保留的槽位

    // 负载均衡
    healthCheckInterval: 30000, // 健康检查间隔
    maxConsecutiveFailures: 5,  // 最大连续失败次数
  },

  ovh: {
    // 并发控制
    maxConcurrent: 8,           // 中等并发数
    maxQueueSize: 500,          // 中等队列容量

    // 速率限制
    maxRequestsPerMinute: 120,  // 每分钟最大请求数
    maxRequestsPerHour: 5000,   // 每小时最大请求数

    // 超时设置
    requestTimeout: 45000,      // 45秒请求超时
    retryDelay: 4000,          // 重试延迟4秒
    maxRetries: 2,             // 最大重试次数

    // 优先级设置
    minPriority: 2,            // 最低优先级要求
    reservedSlots: 2,          // 为高优先级保留的槽位

    // 成本控制
    dailyUsageLimit: 8000,     // 每日使用限制
    costPerRequest: 0.005,     // 每次请求成本
  }
};
```

#### 4.2 动态Pool Size调整机制

```javascript
class DynamicPoolManager {
  constructor(config) {
    this.config = config;
    this.currentLoad = {};
    this.performanceMetrics = {};
    this.adjustmentHistory = [];
  }

  // 根据系统负载动态调整Pool Size
  adjustPoolSize(serviceName) {
    const service = this.config[serviceName];
    const metrics = this.performanceMetrics[serviceName];

    if (!metrics) return service.maxConcurrent;

    let adjustedSize = service.maxConcurrent;

    // 基于成功率调整
    if (metrics.successRate < 0.9) {
      adjustedSize = Math.max(1, Math.floor(adjustedSize * 0.8));
    } else if (metrics.successRate > 0.98) {
      adjustedSize = Math.min(service.maxConcurrent * 1.2, adjustedSize + 2);
    }

    // 基于响应时间调整
    if (metrics.avgResponseTime > service.requestTimeout * 0.8) {
      adjustedSize = Math.max(1, adjustedSize - 1);
    }

    // 基于队列长度调整
    const queueUtilization = metrics.currentQueueSize / service.maxQueueSize;
    if (queueUtilization > 0.8) {
      adjustedSize = Math.min(adjustedSize + 1, service.maxConcurrent * 1.5);
    }

    return Math.floor(adjustedSize);
  }

  // 更新性能指标
  updateMetrics(serviceName, responseTime, success, queueSize) {
    if (!this.performanceMetrics[serviceName]) {
      this.performanceMetrics[serviceName] = {
        totalRequests: 0,
        successfulRequests: 0,
        totalResponseTime: 0,
        currentQueueSize: 0,
        lastUpdateTime: new Date()
      };
    }

    const metrics = this.performanceMetrics[serviceName];
    metrics.totalRequests++;
    metrics.totalResponseTime += responseTime;
    metrics.currentQueueSize = queueSize;

    if (success) {
      metrics.successfulRequests++;
    }

    // 计算成功率和平均响应时间
    metrics.successRate = metrics.successfulRequests / metrics.totalRequests;
    metrics.avgResponseTime = metrics.totalResponseTime / metrics.totalRequests;
    metrics.lastUpdateTime = new Date();
  }
}
```

#### 4.3 并发控制实现

##### 4.3.1 信号量机制

```javascript
class ServiceSemaphore {
  constructor(serviceName, maxConcurrent) {
    this.serviceName = serviceName;
    this.maxConcurrent = maxConcurrent;
    this.currentConcurrent = 0;
    this.waitingQueue = [];
    this.reservedSlots = 0;
  }

  // 获取执行权限
  async acquire(priority = 5) {
    return new Promise((resolve, reject) => {
      // 检查是否有可用槽位
      if (this.currentConcurrent < this.maxConcurrent) {
        this.currentConcurrent++;
        resolve();
        return;
      }

      // 检查是否可以使用保留槽位（高优先级）
      if (priority >= 8 && this.reservedSlots > 0) {
        this.reservedSlots--;
        this.currentConcurrent++;
        resolve();
        return;
      }

      // 加入等待队列
      this.waitingQueue.push({ resolve, reject, priority, timestamp: Date.now() });

      // 按优先级排序等待队列
      this.waitingQueue.sort((a, b) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority; // 高优先级在前
        }
        return a.timestamp - b.timestamp; // 同优先级按时间排序
      });
    });
  }

  // 释放执行权限
  release() {
    this.currentConcurrent--;

    // 处理等待队列
    if (this.waitingQueue.length > 0) {
      const next = this.waitingQueue.shift();
      this.currentConcurrent++;
      next.resolve();
    }
  }

  // 获取当前状态
  getStatus() {
    return {
      serviceName: this.serviceName,
      currentConcurrent: this.currentConcurrent,
      maxConcurrent: this.maxConcurrent,
      waitingCount: this.waitingQueue.length,
      utilization: this.currentConcurrent / this.maxConcurrent
    };
  }
}
```

##### 4.3.2 队列管理器

```javascript
class TranslationQueueManager {
  constructor(poolConfig) {
    this.poolConfig = poolConfig;
    this.semaphores = {};
    this.rateLimiters = {};

    // 初始化各服务的信号量和限流器
    for (const [serviceName, config] of Object.entries(poolConfig)) {
      this.semaphores[serviceName] = new ServiceSemaphore(serviceName, config.maxConcurrent);
      this.rateLimiters[serviceName] = new RateLimiter(config);
    }
  }

  // 选择最佳服务
  async selectBestService(allowedServices, priority) {
    const serviceScores = [];

    for (const serviceName of allowedServices) {
      const semaphore = this.semaphores[serviceName];
      const rateLimiter = this.rateLimiters[serviceName];
      const config = this.poolConfig[serviceName];

      // 检查服务是否可用
      if (!rateLimiter.canMakeRequest()) {
        continue;
      }

      // 检查优先级要求
      if (priority < config.minPriority) {
        continue;
      }

      // 计算服务评分
      const status = semaphore.getStatus();
      const score = this.calculateServiceScore(serviceName, status, priority);

      serviceScores.push({ serviceName, score, status });
    }

    // 按评分排序，选择最佳服务
    serviceScores.sort((a, b) => b.score - a.score);

    return serviceScores.length > 0 ? serviceScores[0].serviceName : null;
  }

  // 计算服务评分
  calculateServiceScore(serviceName, status, priority) {
    const config = this.poolConfig[serviceName];
    let score = 0;

    // 可用性评分（0-40分）
    const availability = 1 - status.utilization;
    score += availability * 40;

    // 优先级匹配评分（0-30分）
    if (priority >= config.minPriority) {
      score += 30;
    }

    // 服务质量评分（0-20分）
    const qualityScore = {
      'gemini': 20,
      'ovh': 15,
      'rm': 10
    };
    score += qualityScore[serviceName] || 0;

    // 成本效益评分（0-10分）
    const costScore = {
      'rm': 10,
      'ovh': 7,
      'gemini': 3
    };
    score += costScore[serviceName] || 0;

    return score;
  }
}
```

### 5. 系统组件设计

#### 5.1 PropTranslationQueue类
```javascript
class PropTranslationQueue {
  constructor(queueCol, translatorManager) {
    this.queueCol = queueCol;
    this.translatorManager = translatorManager;
    this.poolManager = new PoolManager(POOL_CONFIG);
  }
  
  // 添加翻译任务到队列
  async addTranslationTask(prop, triggerSource) {}
  
  // 获取下一批待处理任务
  async getNextBatch(service, batchSize) {}
  
  // 处理翻译任务
  async processTranslationTask(task) {}
  
  // 更新任务状态
  async updateTaskStatus(taskId, status, result) {}
}
```

#### 5.2 翻译工作进程
```javascript
class TranslationWorker {
  constructor(service, queue, poolManager) {
    this.service = service;
    this.queue = queue;
    this.poolManager = poolManager;
  }
  
  // 启动工作进程
  async start() {}
  
  // 处理翻译任务
  async processTasks() {}
  
  // 停止工作进程
  async stop() {}
}
```

### 6. 实施步骤

#### 6.1 第一阶段：基础队列系统
1. 创建PropTranslationQueue类和相关数据结构
2. 修改watchPropAndTranslate.coffee，将翻译请求加入队列而非直接处理
3. 实现基本的队列处理逻辑

#### 6.2 第二阶段：优先级和服务选择
1. 实现触发源识别机制
2. 添加优先级处理逻辑
3. 实现翻译服务选择策略

#### 6.3 第三阶段：并发控制和优化
1. 实现Pool Size限制
2. 添加并发控制机制
3. 实现监控和统计功能

#### 6.4 第四阶段：测试和部署
1. 单元测试和集成测试
2. 性能测试和压力测试
3. 灰度发布和全量部署

### 7. 监控和维护

#### 7.1 监控指标
- 队列长度和处理速度
- 各翻译服务的使用率和成功率
- 平均处理时间和错误率
- 不同优先级任务的处理情况

#### 7.2 告警机制
- 队列积压告警
- 翻译服务异常告警
- 处理超时告警
- 错误率过高告警

### 8. 配置管理和系统集成

#### 8.1 完整配置参数

```javascript
const TRANSLATION_QUEUE_CONFIG = {
  // 队列处理配置
  batchSize: 50,                    // 批处理大小
  processInterval: 5000,            // 处理间隔(ms)
  maxRetryCount: 3,                 // 最大重试次数

  // 优先级配置
  priorityThreshold: 5,             // 优先级阈值
  userClickTimeout: 30000,          // 用户点击超时
  naturalImportTimeout: 120000,     // 自然导入超时

  // 触发源识别配置
  triggerDetection: {
    recentViewWindow: 30 * 60 * 1000,     // 30分钟用户查看窗口
    batchFrequencyThreshold: 10,          // 批量操作频率阈值
    highPriorityBoostWindow: 5 * 60 * 1000, // 5分钟高优先级提升窗口
  },

  // Pool Size配置
  poolSizes: DETAILED_POOL_CONFIG,

  // 监控配置
  monitoring: {
    metricsUpdateInterval: 60000,     // 指标更新间隔
    alertThresholds: {
      queueLength: 1000,              // 队列长度告警阈值
      errorRate: 0.1,                 // 错误率告警阈值
      avgProcessTime: 30000,          // 平均处理时间告警阈值
    }
  },

  // 数据库配置
  database: {
    queueCollection: 'propTranslationQueue',
    metricsCollection: 'translationMetrics',
    indexConfig: {
      priority: { priority: -1, status: 1, createdAt: 1 },
      triggerSource: { triggerSource: 1, priority: -1 },
      processing: { dlShallEndTs: 1, status: 1 }
    }
  }
};
```

#### 8.2 与现有系统的集成点

##### 8.2.1 与watchPropAndTranslate.coffee的集成

```javascript
// 修改现有的_insertOne函数
_insertOne = (prop, cb) -> {
  // ... 现有的验证逻辑 ...

  // 替换直接翻译调用为队列添加
  try {
    const triggerInfo = await determineTriggerSource(prop, changedObj);
    const queueItem = {
      _id: prop._id,
      priority: triggerInfo.priority,
      triggerSource: triggerInfo.triggerSource,
      content: replaceM(prop.m),
      translationServices: getServicesForTrigger(triggerInfo.triggerSource),
      status: 'pending',
      createdAt: new Date(),
      retryCount: 0
    };

    await translationQueue.addTranslationTask(queueItem);
    speedMeter.check({'queuedForTranslation': 1});
  } catch (error) {
    debug.error('Failed to queue translation:', error);
    speedMeter.check({'queueTranslationFailed': 1});
  }

  cb();
};
```

##### 8.2.2 与translatorManager的集成

```javascript
// 扩展现有的TranslatorManager
class EnhancedTranslatorManager extends TranslatorManager {
  constructor(config, queueManager) {
    super(config);
    this.queueManager = queueManager;
    this.metrics = new TranslationMetrics();
  }

  async translateWithQueue(task) {
    const startTime = Date.now();
    let selectedService = null;

    try {
      // 选择最佳服务
      selectedService = await this.queueManager.selectBestService(
        task.translationServices,
        task.priority
      );

      if (!selectedService) {
        throw new Error('No available translation service');
      }

      // 获取信号量
      await this.queueManager.semaphores[selectedService].acquire(task.priority);

      // 执行翻译
      const [result, actualService] = await this.translate(
        task.content,
        [selectedService]
      );

      // 更新指标
      const responseTime = Date.now() - startTime;
      this.queueManager.updateMetrics(selectedService, responseTime, true, 0);

      return { result, service: actualService, responseTime };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      if (selectedService) {
        this.queueManager.updateMetrics(selectedService, responseTime, false, 0);
      }
      throw error;
    } finally {
      if (selectedService) {
        this.queueManager.semaphores[selectedService].release();
      }
    }
  }
}
```

#### 8.3 部署和运维配置

##### 8.3.1 进程管理配置

```bash
# 新增翻译队列处理进程配置
# start.sh -t batch -n propTranslationQueue -cmd 'lib/batchBase.coffee batch/prop/propTranslationQueueProcessor.coffee'

# 进程监控配置
PROCESS_CONFIG = {
  propTranslationQueue: {
    maxMemory: '2GB',
    maxCpu: '80%',
    restartPolicy: 'always',
    healthCheck: {
      interval: 30000,
      timeout: 10000,
      retries: 3
    }
  }
}
```

##### 8.3.2 日志配置

```javascript
const LOGGING_CONFIG = {
  translationQueue: {
    level: 'info',
    file: './logs/translationQueue.log',
    maxSize: '100MB',
    maxFiles: 10,
    format: 'json'
  },
  translationMetrics: {
    level: 'debug',
    file: './logs/translationMetrics.log',
    maxSize: '50MB',
    maxFiles: 5
  }
};
```

### 9. 性能优化和最佳实践

#### 9.1 缓存策略

```javascript
// 翻译结果缓存
class TranslationCache {
  constructor() {
    this.cache = new Map();
    this.maxSize = 10000;
    this.ttl = 24 * 60 * 60 * 1000; // 24小时
  }

  get(content) {
    const key = this.generateKey(content);
    const cached = this.cache.get(key);

    if (cached && (Date.now() - cached.timestamp) < this.ttl) {
      return cached.result;
    }

    return null;
  }

  set(content, result) {
    const key = this.generateKey(content);

    // 清理过期缓存
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
    }

    this.cache.set(key, {
      result,
      timestamp: Date.now()
    });
  }

  generateKey(content) {
    return crypto.createHash('md5').update(content).digest('hex');
  }
}
```

#### 9.2 错误处理和恢复

```javascript
// 错误处理策略
class ErrorHandler {
  static async handleTranslationError(task, error, retryCount) {
    const maxRetries = TRANSLATION_QUEUE_CONFIG.maxRetryCount;

    if (retryCount < maxRetries) {
      // 指数退避重试
      const delay = Math.min(1000 * Math.pow(2, retryCount), 30000);

      setTimeout(async () => {
        await translationQueue.retryTask(task, retryCount + 1);
      }, delay);

      return;
    }

    // 超过重试次数，标记为失败
    await translationQueue.markTaskFailed(task, error.message);

    // 发送告警
    if (task.priority >= 8) {
      await alertManager.sendAlert({
        type: 'translation_failure',
        priority: 'high',
        message: `High priority translation failed: ${task._id}`,
        error: error.message
      });
    }
  }
}
```

## 总结

该解决方案通过深入分析现有代码架构，提供了一个完整的房源翻译队列系统设计：

### 核心优势

1. **智能触发源识别**：基于用户行为日志、Change Stream上下文、MLS导入状态等多维度信息，准确区分用户触发和自然导入
2. **动态Pool Size管理**：根据服务特性、系统负载、性能指标动态调整并发数，最大化资源利用率
3. **优先级驱动调度**：用户点击获得高优先级和优质服务，批量导入使用成本效益服务
4. **完善的监控体系**：实时监控队列状态、服务性能、错误率等关键指标
5. **渐进式迁移**：与现有系统无缝集成，支持平滑迁移和回滚

### 实施效果预期

- **用户体验提升**：用户触发的翻译请求响应时间从5-10秒降低到1-3秒
- **系统稳定性增强**：通过队列缓冲和限流机制，避免翻译服务过载
- **成本优化**：智能服务选择和Pool Size控制，预计可降低30%的翻译成本
- **可扩展性提升**：模块化设计支持新翻译服务的快速接入

该方案充分考虑了现有系统的复杂性和业务需求，提供了一个既实用又可扩展的解决方案。
