# 房源翻译队列系统解决方案

## 需求分析

### 当前问题
- 房源prop.m翻译请求直接同步处理，可能造成性能瓶颈
- 无法根据优先级灵活选择翻译服务
- 缺乏对不同触发源的区分处理机制
- 没有有效的并发控制和资源管理

### 目标需求
1. 房源prop.m的翻译请求改用queue异步处理
2. 高优先级使用gemini，否则使用rm/ovh翻译服务
3. 根据用户点击还是自然import进来区分处理优先级
4. 实现pool size限制，控制并发处理数量
5. gemini需要立刻处理，rm/ovh可以延迟处理

## 现有架构分析

### 当前翻译系统
- **监听机制**: `watchPropAndTranslate.coffee`使用MongoDB change stream监听properties集合变化
- **翻译管理**: `translatorManager.coffee`管理多种翻译服务，支持使用限制和等待队列
- **翻译服务**: 支持gemini、rm、ovh等多种翻译服务，每个服务有maxUsage限制
- **处理方式**: 同步处理，直接调用翻译服务

### Queue参考实现
- **基础架构**: `mlsResourceDownloadQueue.coffee`提供了基于MongoDB的优先级队列实现
- **核心特性**: 支持优先级排序、批量处理、防重复处理机制
- **字段设计**: 使用dlShallEndTs字段防止重复处理，支持priority字段排序

## 解决方案设计

### 1. 翻译队列系统架构

#### 1.1 队列数据结构
```javascript
// propTranslationQueue集合
{
  _id: ObjectId,           // 房源ID
  priority: Number,        // 优先级 (1-10, 10最高)
  triggerSource: String,   // 触发源: 'user_click' | 'natural_import'
  content: String,         // 待翻译内容
  translationServices: Array, // 可用翻译服务列表
  status: String,          // 状态: 'pending' | 'processing' | 'completed' | 'failed'
  createdAt: Date,         // 创建时间
  processStartAt: Date,    // 开始处理时间
  processEndAt: Date,      // 处理结束时间
  retryCount: Number,      // 重试次数
  lastError: String,       // 最后错误信息
  dlShallEndTs: Date       // 处理锁定时间（参考mlsResourceDownloadQueue）
}
```

#### 1.2 优先级策略
- **用户点击触发**: priority = 8-10，使用gemini翻译服务
- **自然import触发**: priority = 1-5，使用rm/ovh翻译服务
- **紧急处理**: priority = 10，立即处理
- **批量处理**: priority = 1-3，可延迟处理

### 2. 触发源识别机制

#### 2.1 识别策略
由于现有代码中没有明确的EP字段，采用以下策略识别触发源：

1. **API调用源识别**
   - 通过请求头或参数识别调用来源
   - 用户交互API调用标记为'user_click'
   - 批量导入/同步标记为'natural_import'

2. **时间窗口策略**
   - 短时间内大量翻译请求视为批量导入
   - 单个或少量请求视为用户触发

3. **用户行为关联**
   - 结合UserLog中的用户行为记录
   - 有用户查看记录的房源优先级提升

#### 2.2 实现方式
```javascript
// 在watchPropAndTranslate.coffee中添加触发源判断逻辑
determineTriggerSource = (prop, changeContext) => {
  // 检查是否有用户最近查看记录
  const hasRecentView = await checkRecentUserView(prop._id);
  
  // 检查批量导入标识
  const isBatchImport = changeContext?.isBatch || false;
  
  // 检查时间窗口内的请求频率
  const requestFrequency = await getRecentRequestFrequency();
  
  if (hasRecentView && !isBatchImport && requestFrequency < THRESHOLD) {
    return 'user_click';
  }
  return 'natural_import';
}
```

### 3. 翻译服务选择策略

#### 3.1 服务分配规则
```javascript
const SERVICE_STRATEGY = {
  'user_click': {
    priority: 8,
    services: ['gemini'],           // 高质量服务
    maxRetry: 2,
    timeout: 30000                  // 30秒超时
  },
  'natural_import': {
    priority: 3,
    services: ['rm', 'ovh'],        // 成本效益服务
    maxRetry: 1,
    timeout: 120000                 // 2分钟超时
  }
}
```

#### 3.2 服务降级机制
- gemini服务不可用时，自动降级到rm/ovh
- 所有服务都不可用时，加入重试队列
- 重试次数超限后标记为失败

### 4. 并发控制和Pool Size限制

#### 4.1 Pool Size配置
```javascript
const POOL_CONFIG = {
  gemini: {
    maxConcurrent: 5,      // gemini最大并发数
    maxQueueSize: 100      // 最大队列长度
  },
  rm: {
    maxConcurrent: 10,     // rm最大并发数
    maxQueueSize: 500
  },
  ovh: {
    maxConcurrent: 8,      // ovh最大并发数
    maxQueueSize: 300
  }
}
```

#### 4.2 并发控制实现
- 使用信号量机制控制每个服务的并发数
- 队列满时拒绝新请求或等待
- 实现优雅降级和负载均衡

### 5. 系统组件设计

#### 5.1 PropTranslationQueue类
```javascript
class PropTranslationQueue {
  constructor(queueCol, translatorManager) {
    this.queueCol = queueCol;
    this.translatorManager = translatorManager;
    this.poolManager = new PoolManager(POOL_CONFIG);
  }
  
  // 添加翻译任务到队列
  async addTranslationTask(prop, triggerSource) {}
  
  // 获取下一批待处理任务
  async getNextBatch(service, batchSize) {}
  
  // 处理翻译任务
  async processTranslationTask(task) {}
  
  // 更新任务状态
  async updateTaskStatus(taskId, status, result) {}
}
```

#### 5.2 翻译工作进程
```javascript
class TranslationWorker {
  constructor(service, queue, poolManager) {
    this.service = service;
    this.queue = queue;
    this.poolManager = poolManager;
  }
  
  // 启动工作进程
  async start() {}
  
  // 处理翻译任务
  async processTasks() {}
  
  // 停止工作进程
  async stop() {}
}
```

### 6. 实施步骤

#### 6.1 第一阶段：基础队列系统
1. 创建PropTranslationQueue类和相关数据结构
2. 修改watchPropAndTranslate.coffee，将翻译请求加入队列而非直接处理
3. 实现基本的队列处理逻辑

#### 6.2 第二阶段：优先级和服务选择
1. 实现触发源识别机制
2. 添加优先级处理逻辑
3. 实现翻译服务选择策略

#### 6.3 第三阶段：并发控制和优化
1. 实现Pool Size限制
2. 添加并发控制机制
3. 实现监控和统计功能

#### 6.4 第四阶段：测试和部署
1. 单元测试和集成测试
2. 性能测试和压力测试
3. 灰度发布和全量部署

### 7. 监控和维护

#### 7.1 监控指标
- 队列长度和处理速度
- 各翻译服务的使用率和成功率
- 平均处理时间和错误率
- 不同优先级任务的处理情况

#### 7.2 告警机制
- 队列积压告警
- 翻译服务异常告警
- 处理超时告警
- 错误率过高告警

### 8. 配置管理

#### 8.1 可配置参数
```javascript
const TRANSLATION_QUEUE_CONFIG = {
  batchSize: 50,                    // 批处理大小
  processInterval: 5000,            // 处理间隔(ms)
  maxRetryCount: 3,                 // 最大重试次数
  priorityThreshold: 5,             // 优先级阈值
  userClickTimeout: 30000,          // 用户点击超时
  naturalImportTimeout: 120000,     // 自然导入超时
  poolSizes: POOL_CONFIG           // Pool大小配置
}
```

## 总结

该解决方案通过引入队列机制，实现了房源翻译的异步处理，支持根据触发源和优先级灵活选择翻译服务，并通过Pool Size限制有效控制系统资源使用。整个系统具有良好的扩展性和可维护性，能够满足当前和未来的业务需求。
